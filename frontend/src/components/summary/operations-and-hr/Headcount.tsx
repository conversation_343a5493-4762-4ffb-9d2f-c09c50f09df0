"use client"

import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Label<PERSON>ist,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  CUSTOM_CHART_COLORS,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRHeadcount } from "@/services/summary"

const TABS = ["Full time", "Part time"]
const MIN_BAR_HEIGHT = 8

const Headcount = () => {
  const { filters } = useSummaryFilters()

  const [selectedTab, setSelectedTab] = useState<string>(TABS[0])

  const { data, isLoading } = useOper<PERSON>AndHRHeadcount({
    type: selectedTab.toLowerCase().replace(" ", "-"),
    filters,
  })

  return (
    <Card
      title="Headcount"
      tabs={TABS}
      selectedTab={selectedTab}
      onTabChange={setSelectedTab}
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <StackedBarChart data={data as unknown as StackedBarData[]} />
    </Card>
  )
}

export default Headcount

export const StackedBarChart = ({
  data,
  chartType = "Bar",
  children,
}: {
  data: StackedBarData[]
  chartType?: ChartType
  children?: React.ReactNode
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    {chartType === "Bar" ? (
      <BarChart data={data}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend
          iconType="circle"
          iconSize={8}
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, top: 0 }}
        />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Bar
              key={key}
              dataKey={key}
              name={key}
              stackId="a"
              fill={
                index < 5
                  ? `var(--color-chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--color-chart-1)"
              }
            >
              <LabelList
                content={({ x, y, width, height, index }) => {
                  const value = data?.[index || 0]?.[key] || 0

                  const isVisible =
                    Number(value) != 0 &&
                    Math.abs(Number(height)) > MIN_BAR_HEIGHT

                  if (!isVisible) return null

                  return (
                    <text
                      x={Number(x) + Number(width) / 2}
                      y={Number(y) + Number(height) / 2}
                      fill="white"
                      fontSize={12}
                      textAnchor="middle"
                      dominantBaseline="central"
                    >
                      {formatAbbreviatedCurrency(Number(value), 0)}
                    </text>
                  )
                }}
              />
            </Bar>
          ))}

        {children}
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend verticalAlign="top" wrapperStyle={{ fontSize: 12, top: 0 }} />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Line
              key={key}
              type="monotone"
              dataKey={key}
              name={key}
              stroke={
                index < 5
                  ? `var(--color-chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--color-chart-1)"
              }
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 5 }}
            >
              <LabelList
                dataKey={key}
                position="top"
                fill="black"
                formatter={(value: number) =>
                  formatAbbreviatedCurrency(value, 0)
                }
                fontSize={12}
              />
            </Line>
          ))}

        {children}
      </LineChart>
    )}
  </ResponsiveContainer>
)
