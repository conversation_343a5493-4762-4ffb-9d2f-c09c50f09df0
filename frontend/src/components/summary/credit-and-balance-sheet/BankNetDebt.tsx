"use client"

import React, { useState } from "react"
import {
  Bar,
  <PERSON>Chart,
  Cell,
  LabelList,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { BankNetDebtData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  ChartTypeSelect,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useCreditAndBalanceSheetBankAndNetDebt } from "@/services/summary"

const BankNetDebt = () => {
  const { data, isLoading } = useCreditAndBalanceSheetBankAndNetDebt()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  return (
    <Card
      title="Bank & Net Debt"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      isLoading={isLoading}
    >
      <BankNetDebtChart data={data} chartType={chartType} />
    </Card>
  )
}

export default BankNetDebt

const MIN_BAR_HEIGHT = 8

const BankNetDebtChart = ({
  data,
  chartType,
}: {
  data: BankNetDebtData[]
  chartType: ChartType
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    {chartType === "Bar" ? (
      <BarChart data={data} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Bar dataKey="bankDebt" name="Bank Debt" fill="var(--chart-1)">
          <Cell fill="var(--chart-1)" />

          <LabelList
            content={({ value, x, y, width, height }) => {
              const isVisible =
                Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

              if (!isVisible) return null

              return (
                <text
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) + Number(height) / 2}
                  fill="white"
                  fontSize={12}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {formatAbbreviatedCurrency(Number(value))}
                </text>
              )
            }}
          />
        </Bar>

        <Bar dataKey="netDebt" name="Net Debt" fill="var(--chart-3)">
          <Cell fill="var(--chart-3)" />

          <LabelList
            content={({ value, x, y, width, height }) => {
              const isVisible =
                Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

              if (!isVisible) return null

              return (
                <text
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) + Number(height) / 2}
                  fill="white"
                  fontSize={12}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {formatAbbreviatedCurrency(Number(value))}
                </text>
              )
            }}
          />
        </Bar>
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Line dataKey="bankDebt" name="Bank Debt" stroke="var(--chart-1)">
          <LabelList
            dataKey="bankDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>

        <Line dataKey="netDebt" name="Net Debt" stroke="var(--chart-3)">
          <LabelList
            dataKey="netDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>
      </LineChart>
    )}
  </ResponsiveContainer>
)
