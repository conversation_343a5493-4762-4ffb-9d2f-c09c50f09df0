from rest_framework.viewsets import ModelViewSet
from datetime import datetime
from collections import defaultdict, OrderedDict
from django.db.models import Decimal<PERSON>ield, Sum, Value, Q
from django.db.models.functions import Coalesce, ExtractYear
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from rest_framework.decorators import action
from rest_framework import status
from fuzzywuzzy import fuzz, process
import re
import traceback
from accounting.models import ChartofAccount, JournalEntryTransaction, Clinic, Employee
from utils.response_template import custom_error_response, custom_success_response

# Cache timeout in seconds (24 hours)
CACHE_TTL = 60 * 60 * 24


class ProfitAndLossViewSet(ModelViewSet):
    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years + 1
            end_year = year
            chart_years = list(range(start_year, end_year + 1))

            # --- Get all relevant chart of accounts in one query ---
            accounts = ChartofAccount.objects.filter(
                entity__currency=currency,
            ).values(
                "id",
                "account_type",
                "account_name",
                "cash_account",
                "bank_debt",
                "total_debt",
            )

            account_info = {}
            revenue_ids = set()
            cos_ids = set()
            ebitda_expense_ids = set()
            all_expense_ids = set()
            bank_debt_ids = set()
            cash_ids = set()

            for a in accounts:
                account_info[a["id"]] = a
                account_type = a["account_type"]
                account_name = a["account_name"].lower()

                if account_type in ["Revenues", "Income"]:
                    revenue_ids.add(a["id"])
                elif account_type == "Cost of sales":
                    cos_ids.add(a["id"])
                elif account_type == "Expenses":
                    all_expense_ids.add(a["id"])
                    # EBITDA excludes certain expense types (same logic as ebitda_chart endpoint)
                    if not any(
                        x in account_name
                        for x in [
                            "depreciation",
                            "amortization",
                            "interest",
                            "management fee",
                        ]
                    ):
                        ebitda_expense_ids.add(a["id"])

                # Bank debt accounts - using bank_debt flag or name matching
                if a["bank_debt"] or (
                    account_type == "Liability"
                    and any(x in account_name for x in ["bank", "debt"])
                ):
                    bank_debt_ids.add(a["id"])

                # Cash accounts
                if a["cash_account"]:
                    cash_ids.add(a["id"])

            # --- Use aggregated queries like other endpoints ---
            relevant_account_ids = list(
                revenue_ids | cos_ids | all_expense_ids | bank_debt_ids | cash_ids
            )

            # Query all journal entries across all years in one go
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=relevant_account_ids,
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(
                    Q(memo__icontains="FOR CLOSING")
                    | Q(chart_of_account__account_name__icontains="interco")
                )
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # --- FTE Constants ---
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            # --- Compute metrics using aggregated data ---
            historical_data = {}

            # Initialize data for all years
            for calc_year in chart_years:
                historical_data[calc_year] = {
                    "gross_revenue": 0.0,
                    "ebitda": 0.0,
                    "net_profit": 0.0,
                    "bank_debt": 0.0,
                    "cash_balance": 0.0,
                    "fte": 0.0,
                }

            # Initialize separate tracking for EBITDA components (same as ebitda_chart endpoint)
            ebitda_by_year = {}
            for calc_year in chart_years:
                ebitda_by_year[calc_year] = {"revenue": 0, "cos": 0, "expenses": 0}

            # Process aggregated entries
            for entry in entries:
                year = entry["year"]
                if year not in chart_years:
                    continue

                acc_id = entry["chart_of_account_id"]
                credit = float(entry["total_credit"] or 0)
                debit = float(entry["total_debit"] or 0)

                if acc_id in revenue_ids:
                    revenue_amount = credit - debit
                    historical_data[year]["gross_revenue"] += revenue_amount
                    ebitda_by_year[year]["revenue"] += revenue_amount
                elif acc_id in cos_ids:
                    cost_of_sales = debit - credit
                    ebitda_by_year[year]["cos"] += cost_of_sales
                    # For net profit, treat cost of sales as expenses (like net_profit_chart)
                    historical_data[year]["net_profit"] -= cost_of_sales
                elif acc_id in all_expense_ids:
                    expense_amount = debit - credit
                    # For net profit, subtract all expenses (like net_profit_chart)
                    historical_data[year]["net_profit"] -= expense_amount
                    # Only add to EBITDA expenses if it's an EBITDA expense (excludes depreciation, etc.)
                    if acc_id in ebitda_expense_ids:
                        ebitda_by_year[year]["expenses"] += expense_amount
                elif acc_id in bank_debt_ids:
                    historical_data[year]["bank_debt"] += credit - debit
                elif acc_id in cash_ids:
                    historical_data[year]["cash_balance"] += debit - credit

            # Calculate final EBITDA using same formula as ebitda_chart endpoint
            for calc_year in chart_years:
                yr_data = ebitda_by_year[calc_year]
                historical_data[calc_year]["ebitda"] = (
                    yr_data["revenue"] - yr_data["cos"] - yr_data["expenses"]
                )
                # Net profit = revenue - all expenses (including cost of sales)
                historical_data[calc_year]["net_profit"] += historical_data[calc_year][
                    "gross_revenue"
                ]

                # FTE calculation
                employees = Employee.objects.filter(
                    Q(joined_date__year__lte=calc_year)
                    & (
                        Q(resignation_date__isnull=True)
                        | Q(resignation_date__year__gt=calc_year)
                    )
                )
                total_fte = sum(FTE_WEIGHTS.get(emp.category, 0.0) for emp in employees)
                historical_data[calc_year]["fte"] = total_fte

            def calc_change(current, previous):
                if previous and previous != 0:
                    return float((current - previous) / previous * 100)
                return 0.0

            current_data = historical_data[year]
            prev_data = historical_data.get(year - 1, {})

            debt_ratio = (
                (current_data["bank_debt"] / current_data["cash_balance"] * 100)
                if current_data["cash_balance"] > 0
                else 0
            )
            cash_ratio = (
                (current_data["cash_balance"] / current_data["bank_debt"] * 100)
                if current_data["bank_debt"] > 0
                else 100
            )

            def chart(key):
                return [
                    {"name": str(y), "value": round(historical_data[y][key], 0)}
                    for y in chart_years
                ]

            overview_data = {
                "gross_revenue": {
                    "value": round(current_data["gross_revenue"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["gross_revenue"],
                            prev_data.get("gross_revenue", 0),
                        ),
                        1,
                    ),
                    "chart_data": chart("gross_revenue"),
                },
                "ebitda": {
                    "value": round(current_data["ebitda"], 0),
                    "percentage": round(
                        calc_change(current_data["ebitda"], prev_data.get("ebitda", 0)),
                        1,
                    ),
                    "chart_data": chart("ebitda"),
                },
                "bank_debt": {
                    "value": round(current_data["bank_debt"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["bank_debt"], prev_data.get("bank_debt", 0)
                        ),
                        1,
                    ),
                    "chart_data": chart("bank_debt"),
                    "additional_info": {
                        "label": "Debt Ratio",
                        "value": f"{debt_ratio:.1f}pts",
                    },
                },
                "cash_balance": {
                    "value": round(current_data["cash_balance"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["cash_balance"],
                            prev_data.get("cash_balance", 0),
                        ),
                        1,
                    ),
                    "chart_data": chart("cash_balance"),
                    "additional_info": {
                        "label": "Cash Ratio",
                        "value": f"{cash_ratio:.1f}pts",
                    },
                },
                "fte": {
                    "value": round(current_data["fte"], 0),
                    "percentage": round(
                        calc_change(current_data["fte"], prev_data.get("fte", 0)), 1
                    ),
                    "chart_data": chart("fte"),
                },
                "net_profit": {
                    "value": round(current_data["net_profit"], 0),
                    "percentage": round(
                        calc_change(
                            current_data["net_profit"], prev_data.get("net_profit", 0)
                        ),
                        1,
                    ),
                    "chart_data": chart("net_profit"),
                },
            }

            return custom_success_response(overview_data)

        except Exception as e:
            import traceback

            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="revenue/chart")
    def revenue_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get chart of accounts for Revenues/Income
            revenue_account_qs = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            )

            # Query all journal entries in range and group by year
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account__in=revenue_account_qs,
                    transaction_date__year__gte=start_year - 1,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Process results
            revenue_by_year = {}
            for e in entries:
                year = e["year"]
                revenue_by_year[year] = e["total_credit"] - e["total_debit"]

            # Prepare final yearly chart data
            yearly_data = []
            for y in range(start_year, end_year + 1):
                revenue = revenue_by_year.get(y, 0)
                prev_revenue = revenue_by_year.get(y - 1, 0)

                percentage = (
                    float((revenue - prev_revenue) / prev_revenue * 100)
                    if prev_revenue
                    else 0.0
                )

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(revenue, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ebitda/chart")
    def ebitda_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get all relevant chart of accounts in one query
            accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income", "Cost of sales", "Expenses"],
                entity__currency=currency,
            ).values("id", "account_type", "account_name")

            account_info = {}
            revenue_ids = set()
            cos_ids = set()
            expense_ids = set()

            for a in accounts:
                account_info[a["id"]] = a
                if a["account_type"] in ["Revenues", "Income"]:
                    revenue_ids.add(a["id"])
                elif a["account_type"] == "Cost of sales":
                    cos_ids.add(a["id"])
                elif a["account_type"] == "Expenses":
                    name = a["account_name"].lower()
                    if not any(
                        x in name
                        for x in [
                            "depreciation",
                            "amortization",
                            "interest",
                            "management fee",
                        ]
                    ):
                        expense_ids.add(a["id"])

            relevant_account_ids = list(revenue_ids | cos_ids | expense_ids)

            # Query all journal entries across all years in one go
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=relevant_account_ids,
                    transaction_date__year__gte=start_year - 1,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Calculate EBITDA per year
            ebitda_by_year = {}

            for entry in entries:
                year = entry["year"]
                acc_id = entry["chart_of_account_id"]

                credit = entry["total_credit"]
                debit = entry["total_debit"]

                if year not in ebitda_by_year:
                    ebitda_by_year[year] = {"revenue": 0, "cos": 0, "expenses": 0}

                if acc_id in revenue_ids:
                    ebitda_by_year[year]["revenue"] += credit - debit
                elif acc_id in cos_ids:
                    ebitda_by_year[year]["cos"] += debit - credit
                elif acc_id in expense_ids:
                    ebitda_by_year[year]["expenses"] += debit - credit

            # Format chart data
            yearly_data = []

            for y in range(start_year, end_year + 1):
                yr_data = ebitda_by_year.get(y, {"revenue": 0, "cos": 0, "expenses": 0})
                ebitda = yr_data["revenue"] - yr_data["cos"] - yr_data["expenses"]
                prev_yr_data = ebitda_by_year.get(
                    y - 1, {"revenue": 0, "cos": 0, "expenses": 0}
                )
                prev_ebitda = (
                    prev_yr_data["revenue"]
                    - prev_yr_data["cos"]
                    - prev_yr_data["expenses"]
                )

                if prev_ebitda:
                    percentage = ((ebitda - prev_ebitda) / prev_ebitda) * 100
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(ebitda, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="net-profit/chart")
    def net_profit_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            revenue_account_types = ["Revenues", "Income"]
            expense_account_types = ["Expenses", "Cost of sales"]

            # Get relevant accounts
            accounts = ChartofAccount.objects.filter(
                account_type__in=revenue_account_types + expense_account_types,
                entity__currency=currency,
            ).values("id", "account_type")

            account_map = {a["id"]: a["account_type"] for a in accounts}
            account_ids = list(account_map.keys())

            # Query all transactions within range, excluding interco and closing
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=account_ids,
                    transaction_date__year__gte=start_year - 1,
                    transaction_date__year__lte=end_year,
                )
                .exclude(
                    Q(memo__icontains="FOR CLOSING")
                    | Q(chart_of_account__account_name__icontains="interco")
                )
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Group and calculate totals
            net_profit_by_year = {}

            for entry in entries:
                acc_type = account_map[entry["chart_of_account_id"]]
                year = entry["year"]
                credit = float(entry["total_credit"] or 0)
                debit = float(entry["total_debit"] or 0)

                if year not in net_profit_by_year:
                    net_profit_by_year[year] = {"revenue": 0, "expense": 0}

                if acc_type in revenue_account_types:
                    net_profit_by_year[year]["revenue"] += credit - debit
                else:
                    net_profit_by_year[year]["expense"] += debit - credit

            # Prepare response
            yearly_data = []
            for y in range(start_year, end_year + 1):
                current = net_profit_by_year.get(y, {"revenue": 0, "expense": 0})
                prev = net_profit_by_year.get(y - 1, {"revenue": 0, "expense": 0})

                net_profit = current["revenue"] - current["expense"]
                prev_net = prev["revenue"] - prev["expense"]

                if prev_net:
                    percentage = ((net_profit - prev_net) / prev_net) * 100
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(net_profit, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(
        detail=False, methods=["get"], url_path="revenue-breakdown/by-segment/chart"
    )
    def revenue_breakdown_by_segment_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Load all revenue COA accounts with clinic_code
            chart_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values("id", "clinic_code")

            coa_id_to_clinic = {c["id"]: c["clinic_code"] for c in chart_accounts}
            coa_ids = list(coa_id_to_clinic.keys())

            # Map clinic code to segment
            clinic_code_to_segment = dict(Clinic.objects.values_list("code", "segment"))

            # Query all journal entries at once
            start_date = datetime(start_year, 1, 1)
            end_date = datetime(end_year, 12, 31)

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=coa_ids,
                    transaction_date__range=[start_date, end_date],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Group and compute revenue by year/segment
            revenue_by_year_segment = defaultdict(lambda: defaultdict(float))
            all_segments = set()

            for e in entries:
                year = e["year"]
                coa_id = e["chart_of_account_id"]
                clinic_code = coa_id_to_clinic.get(coa_id)
                segment = clinic_code_to_segment.get(clinic_code, "Others")

                amount = float(e["total_credit"] or 0) - float(e["total_debit"] or 0)
                revenue_by_year_segment[year][segment] += amount
                all_segments.add(segment)

            # Sort segments
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build final data
            formatted_data = []

            for y in range(start_year, end_year + 1):
                seg_data = revenue_by_year_segment.get(y, {})
                total = sum(seg_data.values())
                record = {"name": f"{y}", "total": round(total, 2)}
                for seg in final_segments:
                    record[seg] = round(seg_data.get(seg, 0.0), 2)

                ordered_record = OrderedDict(
                    [("name", record["name"]), ("total", record["total"])]
                    + [(seg, record[seg]) for seg in final_segments]
                )
                formatted_data.append(ordered_record)

            return custom_success_response(formatted_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="revenue-breakdown/by-clinic/chart")
    def revenue_breakdown_by_clinic_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Load revenue COA accounts with clinic_code
            chart_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values("id", "clinic_code")

            coa_id_to_clinic_code = {c["id"]: c["clinic_code"] for c in chart_accounts}
            coa_ids = list(coa_id_to_clinic_code.keys())

            # Map clinic_code to clinic_name
            clinic_code_to_name = dict(Clinic.objects.values_list("code", "name"))

            # Fetch all journal entries once
            start_date = datetime(start_year, 1, 1)
            end_date = datetime(end_year, 12, 31)

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=coa_ids,
                    transaction_date__range=[start_date, end_date],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Group revenue by year and clinic name
            revenue_by_year_clinic = defaultdict(lambda: defaultdict(float))
            all_clinics = set()

            for e in entries:
                y = e["year"]
                coa_id = e["chart_of_account_id"]
                clinic_code = coa_id_to_clinic_code.get(coa_id)
                clinic_name = clinic_code_to_name.get(clinic_code, "Unassigned")

                amount = float(e["total_credit"] or 0) - float(e["total_debit"] or 0)
                revenue_by_year_clinic[y][clinic_name] += amount
                all_clinics.add(clinic_name)

            # Sort clinics
            final_clinics = sorted([c for c in all_clinics if c != "Unassigned"])
            if "Unassigned" in all_clinics:
                final_clinics.append("Unassigned")

            # Format output
            formatted_data = []
            for y in range(start_year, end_year + 1):
                clinic_data = revenue_by_year_clinic.get(y, {})
                total = sum(clinic_data.values())
                record = {"name": str(y), "total": round(total, 2)}
                for clinic in final_clinics:
                    record[clinic] = round(clinic_data.get(clinic, 0.0), 2)

                ordered_record = OrderedDict(
                    [("name", record["name"]), ("total", record["total"])]
                    + [(clinic, record[clinic]) for clinic in final_clinics]
                )
                formatted_data.append(ordered_record)

            return custom_success_response(formatted_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class CreditAndBalanceSheetViewSet(ModelViewSet):
    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="total-debt/chart")
    def total_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = (
                year - last_n_years - 1
            )  # include one earlier year for % change
            end_year = year

            # Get all relevant debt accounts
            debt_account_ids = ChartofAccount.objects.filter(
                total_debt=True, entity__currency=currency
            ).values_list("id", flat=True)

            # Use database aggregation for performance
            debt_entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=debt_account_ids,
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values("transaction_date__year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Create year => debt value map
            debt_by_year = {
                e["transaction_date__year"]: float(e["total_credit"])
                - float(e["total_debit"])
                for e in debt_entries
            }

            def pct_change(current, previous):
                if previous == 0:
                    return 100.0 if current else 0.0
                return round((current - previous) / previous * 100, 1)

            # Format final response
            response = []
            for y in range(start_year + 1, end_year + 1):
                current = debt_by_year.get(y, 0.0)
                previous = debt_by_year.get(y - 1, 0.0)
                response.append(
                    {
                        "name": str(y),
                        "amount": round(current, 2),
                        "percentage": pct_change(current, previous),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="bank-and-net-debt/chart")
    def bank_and_net_debt_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 3))

            start_year = year - last_n_years
            end_year = year

            # Get account IDs for bank and net debt
            bank_debt_ids = set(
                ChartofAccount.objects.filter(
                    bank_debt=True, entity__currency=currency
                ).values_list("id", flat=True)
            )
            net_debt_ids = set(
                ChartofAccount.objects.filter(
                    total_debt=True, entity__currency=currency
                ).values_list("id", flat=True)
            )

            relevant_ids = bank_debt_ids | net_debt_ids

            # Fetch and group all entries
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=relevant_ids,
                    transaction_date__year__gte=start_year,
                    transaction_date__year__lte=end_year,
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values("transaction_date__year", "chart_of_account_id")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Aggregate results
            yearly_totals = defaultdict(lambda: {"bankDebt": 0.0, "netDebt": 0.0})

            for entry in entries:
                year_key = entry["transaction_date__year"]
                account_id = entry["chart_of_account_id"]
                net = float(entry["total_credit"]) - float(entry["total_debit"])

                if account_id in bank_debt_ids:
                    yearly_totals[year_key]["bankDebt"] += net
                if account_id in net_debt_ids:
                    yearly_totals[year_key]["netDebt"] += net

            # Final formatted response
            response = []
            for y in range(start_year, end_year + 1):
                response.append(
                    {
                        "name": str(y),
                        "bankDebt": round(yearly_totals[y]["bankDebt"], 2),
                        "netDebt": round(yearly_totals[y]["netDebt"], 2),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="current-cash-balance/chart")
    def current_cash_balance_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            prev_year = year - 1

            # Fetch all relevant accounts in a single query
            accounts = ChartofAccount.objects.filter(entity__currency=currency).only(
                "id", "account_type", "cash_account", "bank_debt", "account_name"
            )

            # Categorize accounts
            starting_cash_ids = set()
            revenue_ids = set()
            expense_ids = set()
            capex_ids = set()
            share_loan_ids = set()
            bank_loan_ids = set()
            dividend_ids = set()

            for acc in accounts:
                name_lower = acc.account_name.lower()
                if acc.account_type == "Current Asset" and acc.cash_account:
                    starting_cash_ids.add(acc.id)
                elif acc.account_type in {"Income", "Revenues"}:
                    revenue_ids.add(acc.id)
                elif acc.account_type in {"Expenses", "Expense"}:
                    expense_ids.add(acc.id)
                elif (
                    acc.account_type in {"Asset", "Assets", "Non-current Asset"}
                    and not acc.cash_account
                ):
                    capex_ids.add(acc.id)
                elif acc.account_type == "Equity" and (
                    "share" in name_lower or "loan" in name_lower
                ):
                    share_loan_ids.add(acc.id)
                elif acc.bank_debt:
                    bank_loan_ids.add(acc.id)
                elif acc.account_type == "Equity" and "dividend" in name_lower:
                    dividend_ids.add(acc.id)

            all_ids = (
                starting_cash_ids
                | revenue_ids
                | expense_ids
                | capex_ids
                | share_loan_ids
                | bank_loan_ids
                | dividend_ids
            )

            # Fetch all relevant journal entries at once
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_ids,
                    transaction_date__year__in=[prev_year, year],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            grouped = defaultdict(float)

            for e in entries:
                acc_id = e["chart_of_account_id"]
                entry_year = e["transaction_date"].year
                debit = float(e["reporting_debit_amount"] or 0)
                credit = float(e["reporting_credit_amount"] or 0)
                net = debit - credit

                if entry_year == prev_year and acc_id in starting_cash_ids:
                    grouped["starting_cash"] += net
                elif entry_year == year:
                    if acc_id in revenue_ids:
                        grouped["revenue"] += -net
                    elif acc_id in expense_ids:
                        grouped["expense"] += net
                    elif acc_id in capex_ids:
                        grouped["capex"] += net
                    elif acc_id in share_loan_ids:
                        grouped["share_loans"] += net
                    elif acc_id in bank_loan_ids:
                        grouped["bank_loans"] += net
                    elif acc_id in dividend_ids:
                        grouped["dividends"] += -abs(net)

            # Step-by-step calculations
            start_cash = grouped["starting_cash"]
            ocf = grouped["revenue"] - grouped["expense"]
            capex = grouped["capex"]
            share_loans = grouped["share_loans"]
            bank_loans = grouped["bank_loans"]
            dividends = grouped["dividends"]

            # Waterfall chart structure
            flows = [
                {
                    "name": str(prev_year),
                    "amount": round(start_cash, 2),
                    "remaining": 0,
                },
                {"name": "OCF", "amount": round(ocf, 2)},
                {"name": "CAPEX", "amount": round(-capex, 2)},
                {"name": "Share<br/>Loans", "amount": round(share_loans, 2)},
                {"name": "Bank<br/>Loan", "amount": round(bank_loans, 2)},
                {"name": "Dividends", "amount": round(dividends, 2)},
            ]

            running_total = start_cash
            for step in flows[1:]:
                step["remaining"] = round(running_total, 2)
                running_total += step["amount"]

            flows.append(
                {
                    "name": str(year),
                    "amount": round(running_total, 2),
                    "remaining": 0,
                }
            )

            return custom_success_response(flows)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ocf/chart")
    def ocf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Account categorization config
            REVENUE_TYPES = {"Income", "Revenues"}
            EXPENSE_TYPES = {"Expenses", "Cost of sales"}
            NON_CASH_EXPENSES = {"Depreciation", "Amortization"}
            CURRENT_ASSET_TYPES = {"Current Asset"}
            CURRENT_LIABILITY_TYPES = {"Current Liability"}

            # Fetch and categorize all relevant accounts in a single query
            accounts = ChartofAccount.objects.filter(
                entity__currency=currency, is_postable=True, frozen=False
            ).only("id", "account_type", "account_name")

            revenue_ids = set()
            expense_ids = set()
            asset_ids = set()
            liability_ids = set()
            non_cash_ids = set()

            for acc in accounts:
                if acc.account_type in REVENUE_TYPES:
                    revenue_ids.add(acc.id)
                elif acc.account_type in EXPENSE_TYPES:
                    expense_ids.add(acc.id)
                elif acc.account_type in CURRENT_ASSET_TYPES:
                    asset_ids.add(acc.id)
                elif acc.account_type in CURRENT_LIABILITY_TYPES:
                    liability_ids.add(acc.id)
                if any(
                    term.lower() in acc.account_name.lower()
                    for term in NON_CASH_EXPENSES
                ):
                    non_cash_ids.add(acc.id)

            all_ids = (
                revenue_ids | expense_ids | asset_ids | liability_ids | non_cash_ids
            )

            # Fetch journal entries in a single query
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_ids,
                    transaction_date__range=[
                        datetime(start_year - 1, 1, 1),
                        datetime(end_year, 12, 31),
                    ],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            # Process entries
            yearly_entries = defaultdict(list)
            closing_balances = defaultdict(lambda: defaultdict(float))

            for e in entries:
                acc_id = e["chart_of_account_id"]
                date = e["transaction_date"]
                debit = float(e["reporting_debit_amount"] or 0)
                credit = float(e["reporting_credit_amount"] or 0)
                year_key = date.year

                yearly_entries[year_key].append((acc_id, debit, credit))

                # Pre-calculate cumulative closing balances
                balance = debit - credit
                for y in range(start_year - 1, end_year + 1):
                    if date <= datetime(y, 12, 31).date():
                        closing_balances[y][acc_id] += balance

            # Compute results per year
            results = []

            for y in range(start_year, end_year + 1):
                revenue = expense = non_cash = 0

                for acc_id, debit, credit in yearly_entries.get(y, []):
                    if acc_id in revenue_ids:
                        revenue += credit - debit
                    elif acc_id in expense_ids:
                        expense += debit - credit
                    elif acc_id in non_cash_ids:
                        non_cash += debit - credit

                net_income = revenue - expense

                delta_assets = sum(
                    closing_balances[y].get(a, 0) - closing_balances[y - 1].get(a, 0)
                    for a in asset_ids
                )
                delta_liabilities = sum(
                    closing_balances[y].get(l, 0) - closing_balances[y - 1].get(l, 0)
                    for l in liability_ids
                )

                ocf = net_income + non_cash - delta_assets + delta_liabilities
                results.append({"name": str(y), "value": round(ocf, 2)})

            return custom_success_response(results)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="fcf/chart")
    def fcf_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Define account categories
            REVENUE_TYPES = {"Income", "Revenues"}
            EXPENSE_TYPES = {"Expenses", "Cost of sales"}
            NON_CASH_EXPENSES = {"Depreciation", "Amortization"}
            CURRENT_ASSET_TYPES = {"Current Asset"}
            CURRENT_LIABILITY_TYPES = {"Current Liability"}
            CAPEX_TYPES = {"Non-current Asset", "Asset", "Assets"}

            # Get all relevant accounts in one query
            accounts = ChartofAccount.objects.filter(
                entity__currency=currency,
                is_postable=True,
                frozen=False,
            ).only("id", "account_type", "account_name")

            # Categorize accounts in memory
            revenue_ids, expense_ids, non_cash_ids = set(), set(), set()
            asset_ids, liability_ids, capex_ids = set(), set(), set()

            for acc in accounts:
                acc_type = acc.account_type
                name_lower = acc.account_name.lower()
                if acc_type in REVENUE_TYPES:
                    revenue_ids.add(acc.id)
                elif acc_type in EXPENSE_TYPES:
                    expense_ids.add(acc.id)
                elif acc_type in CURRENT_ASSET_TYPES:
                    asset_ids.add(acc.id)
                elif acc_type in CURRENT_LIABILITY_TYPES:
                    liability_ids.add(acc.id)
                elif acc_type in CAPEX_TYPES:
                    capex_ids.add(acc.id)

                if any(nc.lower() in name_lower for nc in NON_CASH_EXPENSES):
                    non_cash_ids.add(acc.id)

            all_account_ids = (
                revenue_ids
                | expense_ids
                | capex_ids
                | asset_ids
                | liability_ids
                | non_cash_ids
            )

            # Fetch entries for date range and IDs
            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=all_account_ids,
                    transaction_date__range=[
                        datetime(start_year - 1, 1, 1),
                        datetime(end_year, 12, 31),
                    ],
                )
                .exclude(memo__icontains="FOR CLOSING")
                .values(
                    "chart_of_account_id",
                    "transaction_date",
                    "reporting_debit_amount",
                    "reporting_credit_amount",
                )
            )

            # Preprocess entries
            yearly_entries = defaultdict(list)
            cumulative_balances = defaultdict(lambda: defaultdict(float))

            for entry in entries:
                acc_id = entry["chart_of_account_id"]
                date = entry["transaction_date"]
                debit = float(entry["reporting_debit_amount"] or 0)
                credit = float(entry["reporting_credit_amount"] or 0)
                net_amount = debit - credit
                year_key = date.year

                yearly_entries[year_key].append((acc_id, debit, credit))

                # Update cumulative balances up to each year (only once)
                for y in range(start_year - 1, end_year + 1):
                    if date <= datetime(y, 12, 31).date():
                        cumulative_balances[y][acc_id] += net_amount

            # Calculate FCF by year
            fcf_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                revenue = expense = non_cash = capex = 0

                for acc_id, debit, credit in yearly_entries.get(y, []):
                    if acc_id in revenue_ids:
                        revenue += credit - debit
                    elif acc_id in expense_ids:
                        expense += debit - credit
                    elif acc_id in non_cash_ids:
                        non_cash += debit - credit
                    elif acc_id in capex_ids:
                        capex += debit - credit

                net_income = revenue - expense

                # Working capital delta
                delta_assets = sum(
                    cumulative_balances[y].get(acc, 0)
                    - cumulative_balances[y - 1].get(acc, 0)
                    for acc in asset_ids
                )
                delta_liabilities = sum(
                    cumulative_balances[y].get(acc, 0)
                    - cumulative_balances[y - 1].get(acc, 0)
                    for acc in liability_ids
                )

                ocf = net_income + non_cash - delta_assets + delta_liabilities
                fcf = ocf - capex
                fcf_by_year[y] = fcf

            # Final response with % change
            response = []
            for y in range(start_year, end_year + 1):
                current = fcf_by_year.get(y, 0)
                previous = fcf_by_year.get(y - 1, 0)
                percent = (
                    ((current - previous) / abs(previous) * 100) if previous else 0.0
                )

                response.append(
                    {
                        "name": str(y),
                        "amount": round(current, 2),
                        "percentage": round(percent, 1),
                    }
                )

            return custom_success_response(response)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class OperationsAndHRViewSet(ModelViewSet):
    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 4))
            min_year = year - last_n_years + 1
            chart_years = list(range(min_year, year + 1))

            # Get revenue account IDs
            revenue_account_ids = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            # Use aggregated query approach like other endpoints
            revenue_entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=revenue_account_ids,
                    transaction_date__year__gte=min_year,
                    transaction_date__year__lte=year,
                )
                .exclude(
                    Q(memo__icontains="FOR CLOSING")
                    | Q(chart_of_account__account_name__icontains="interco")
                )
                .annotate(year=ExtractYear("transaction_date"))
                .values("year")
                .annotate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )
            )

            # Process revenue data
            revenue_by_year = {}
            for entry in revenue_entries:
                y = entry["year"]
                revenue_by_year[y] = float(entry["total_credit"]) - float(
                    entry["total_debit"]
                )

            def get_revenue(y):
                return revenue_by_year.get(y, 0)

            # Pre-compute historical data for all years using optimized approach
            historical_data = {}

            VOLUNTARY_REASONS = {
                "Back to School",
                "Better Benefits",
                "Career Progression",
                "End of Contract",
                "Family Reasons",
                "Fellow Colleagues",
                "Further Education",
                "Health Reasons",
                "Job Mismatch",
                "Remunerations",
                "Resigned",
                "Supervisor / Management",
            }

            INVOLUNTARY_REASONS = {
                "Company Direction",
                "Dismissed",
                "Not for Rehire",
                "Terminated",
            }

            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            def safe_div(numerator, denominator):
                return float(numerator) / denominator if denominator else 0

            def pct_change(current, previous):
                if not previous:
                    return 100 if current else 0
                return round((current - previous) / previous * 100, 1)

            # OPTIMIZED: Single query approach to avoid N+1 problem
            # Fetch ALL relevant employees once
            all_employees = Employee.objects.filter(
                Q(joined_date__year__lte=year)
                & (
                    Q(resignation_date__isnull=True)
                    | Q(resignation_date__year__gte=min_year)
                )
            ).values(
                "joined_date",
                "resignation_date",
                "resignation_reason",
                "category",
                "department",
            )

            # Fetch ALL resignations in the period once
            all_resignations = Employee.objects.filter(
                resignation_date__year__gte=min_year, resignation_date__year__lte=year
            ).values("resignation_date", "resignation_reason")

            # Pre-compute resignation data for all years
            resignations_by_year = defaultdict(list)

            # Group resignations by year
            for resignation in all_resignations:
                resign_year = resignation["resignation_date"].year
                resignations_by_year[resign_year].append(
                    resignation["resignation_reason"]
                )

            # Calculate metrics for each year using pre-fetched data
            for calc_year in chart_years:
                # Filter employees active in this year from pre-fetched data
                active_employees = [
                    emp
                    for emp in all_employees
                    if emp["joined_date"].year <= calc_year
                    and (
                        not emp["resignation_date"]
                        or emp["resignation_date"].year > calc_year
                    )
                ]

                # Calculate employee metrics
                total_employees = len(active_employees)
                total_fte = sum(
                    FTE_WEIGHTS.get(emp["category"], 0.0) for emp in active_employees
                )
                doctors = sum(
                    1 for emp in active_employees if emp["department"] == "Doctor"
                )

                # Get revenue for this year
                revenue = get_revenue(calc_year)

                # Calculate per-employee metrics
                rev_per_fte = safe_div(revenue, total_fte)
                rev_per_doc = safe_div(revenue, doctors)

                # Calculate attrition rates using pre-grouped data
                year_resignations = resignations_by_year.get(calc_year, [])
                voluntary_count = sum(
                    1 for reason in year_resignations if reason in VOLUNTARY_REASONS
                )
                involuntary_count = sum(
                    1 for reason in year_resignations if reason in INVOLUNTARY_REASONS
                )

                pos_rate = (
                    (voluntary_count / total_employees * 100) if total_employees else 0
                )
                neg_rate = (
                    (involuntary_count / total_employees * 100)
                    if total_employees
                    else 0
                )

                historical_data[calc_year] = {
                    "revenue_per_fte": round(rev_per_fte, 0),
                    "revenue_per_doctor": round(rev_per_doc, 0),
                    "employees": total_employees,
                    "positive_attrition_rate": round(pos_rate, 1),
                    "negative_attrition_rate": round(neg_rate, 1),
                }

            # Current & Previous year data
            curr = historical_data[year]
            prev = historical_data.get(
                year - 1,
                {
                    "revenue_per_fte": 0,
                    "revenue_per_doctor": 0,
                    "employees": 0,
                    "positive_attrition_rate": 0,
                    "negative_attrition_rate": 0,
                },
            )

            overview_data = {
                "revenue_per_fte": {
                    "value": curr["revenue_per_fte"],
                    "percentage": pct_change(
                        curr["revenue_per_fte"], prev["revenue_per_fte"]
                    ),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["revenue_per_fte"]}
                        for y in chart_years
                    ],
                },
                "revenue_per_doctor": {
                    "value": curr["revenue_per_doctor"],
                    "percentage": pct_change(
                        curr["revenue_per_doctor"], prev["revenue_per_doctor"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["revenue_per_doctor"],
                        }
                        for y in chart_years
                    ],
                },
                "employees": {
                    "value": curr["employees"],
                    "percentage": pct_change(curr["employees"], prev["employees"]),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["employees"]}
                        for y in chart_years
                    ],
                },
                "positive_attrition_rate": {
                    "value": curr["positive_attrition_rate"],
                    "percentage": pct_change(
                        curr["positive_attrition_rate"], prev["positive_attrition_rate"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["positive_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
                "negative_attrition_rate": {
                    "value": curr["negative_attrition_rate"],
                    "percentage": pct_change(
                        curr["negative_attrition_rate"], prev["negative_attrition_rate"]
                    ),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["negative_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
            }

            return custom_success_response(overview_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="clinics/chart")
    def clinics_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Build clinic_code → segment map
            clinics = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "code", "segment"
            )
            clinic_segment_map = {c["code"]: c["segment"] or "Others" for c in clinics}
            valid_clinic_codes = set(clinic_segment_map.keys())

            # Get relevant revenue accounts
            coa_qs = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
                clinic_code__in=valid_clinic_codes,
            ).values("id", "clinic_code")

            coa_id_to_clinic = {c["id"]: c["clinic_code"] for c in coa_qs}
            coa_ids = list(coa_id_to_clinic.keys())

            # Query all relevant journal entries in one go
            start_date = datetime(start_year, 1, 1)
            end_date = datetime(end_year, 12, 31)

            entries = (
                JournalEntryTransaction.objects.filter(
                    chart_of_account_id__in=coa_ids,
                    transaction_date__range=(start_date, end_date),
                )
                .exclude(memo__icontains="FOR CLOSING")
                .annotate(year=ExtractYear("transaction_date"))
                .values("year", "chart_of_account_id")
                .distinct()
            )

            # Step 1: collect distinct (year, clinic_code)
            year_clinic_pairs = set()
            for e in entries:
                y = e["year"]
                coa_id = e["chart_of_account_id"]
                clinic_code = coa_id_to_clinic.get(coa_id)
                if clinic_code:
                    year_clinic_pairs.add((y, clinic_code))

            # Step 2: aggregate by segment
            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for y, clinic_code in year_clinic_pairs:
                segment = clinic_segment_map.get(clinic_code, "Others")
                if segment == "Corporate":
                    continue
                chart_data[y][segment] += 1
                chart_data[y]["total"] += 1
                all_segments.add(segment)

            # Sort segments
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Format result
            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for seg in final_segments:
                    row[seg] = chart_data[y].get(seg, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="doctors/chart")
    def doctors_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Prepare clinic name → segment map (normalized)
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_segment_map = {
                normalize_clinic_name(c["name"]): c["segment"] or "Others"
                for c in clinic_qs
            }

            # Filter doctors who joined before or during the period
            employees = Employee.objects.filter(
                department="Doctor", joined_date__year__lte=end_year
            ).values("clinic_name", "joined_date", "resignation_date")

            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                clinic_name = emp["clinic_name"]
                segment = match_clinic_segment(clinic_name, normalized_segment_map)

                all_segments.add(segment)

                joined_year = emp["joined_date"].year
                resigned_year = (
                    emp["resignation_date"].year if emp["resignation_date"] else None
                )

                for y in range(start_year, end_year + 1):
                    if joined_year <= y and (
                        resigned_year is None or resigned_year > y
                    ):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Sort segments, put "Others" at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for seg in final_segments:
                    row[seg] = chart_data[y].get(seg, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="fte/chart")
    def fte_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # FTE conversion weights by employment type
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            # Department to FTE category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            def get_category(department: str) -> str:
                for category, departments in CATEGORY_MAP.items():
                    if department in departments:
                        return category
                return "Other"

            # Preload all relevant employees
            employees = Employee.objects.filter(joined_date__year__lte=end_year).only(
                "department", "category", "joined_date", "resignation_date"
            )

            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                department = (emp.department or "").strip()
                emp_type = (emp.category or "").strip()
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                if fte_value == 0:
                    continue  # skip unknown types

                category = get_category(department)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if (
                        joined_year
                        and joined_year <= y
                        and (not resigned_year or resigned_year > y)
                    ):
                        chart_data[y][category] += fte_value
                        chart_data[y]["total"] += fte_value

            # Final result formatting
            result = []
            all_categories = list(CATEGORY_MAP.keys()) + ["Other"]

            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for cat in all_categories:
                    row[cat] = round(chart_data[y].get(cat, 0.0), 2)
                row["total"] = round(chart_data[y].get("total", 0.0), 2)
                result.append(row)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="headcount/chart")
    def headcount_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))
            emp_type = (
                request.query_params.get("type", "full-time").replace("-", " ").lower()
            )

            start_year = year - last_n_years
            end_year = year

            # Get clinic name → segment map
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_to_segment = {
                normalize_clinic_name(c["name"]): c["segment"] or "Others"
                for c in clinic_qs
            }

            # Determine category filter keyword
            category_filter = "Part Time" if "part" in emp_type else "Full Time"

            # Filter employees with matching type and joining before end year
            employees = Employee.objects.filter(
                category__icontains=category_filter,
                joined_date__year__lte=end_year,
            ).only("clinic_name", "joined_date", "resignation_date")

            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                segment = match_clinic_segment(emp.clinic_name, normalized_to_segment)
                all_segments.add(segment)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if joined_year <= y and (not resigned_year or resigned_year > y):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Segment ordering with 'Others' at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build final result
            result = []
            for y in range(start_year, end_year + 1):
                row = {"name": str(y)}
                for segment in final_segments:
                    row[segment] = chart_data[y].get(segment, 0)
                row["total"] = chart_data[y].get("total", 0)
                result.append(row)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="org-chart")
    def org_chart(self, request):
        try:
            # Fetch only needed fields
            employees = Employee.objects.filter(resignation_date__isnull=True).only(
                "clinic_name", "department", "occupation", "code"
            )

            # clinic_name -> department_group -> occupation -> list of codes
            hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))

            department_mapping = {
                "Doctor": "Doctor",
                "Clinical": "Clinical",
                "Front Desk": "Front Desk",
            }

            for emp in employees:
                clinic = emp.clinic_name or "Unknown"
                department = emp.department
                occupation = emp.occupation or "Unknown"
                code = emp.code or "N/A"

                if department in department_mapping:
                    group = department_mapping[department]
                    hierarchy[clinic][group][occupation].append(code)

            def build_tree(name, children_dict):
                """Recursively builds the tree structure for org chart"""
                children = []
                for key, val in children_dict.items():
                    if isinstance(val, dict):
                        children.append(build_tree(key, val))
                    elif isinstance(val, list):
                        children.append(
                            {"name": key, "children": [{"name": code} for code in val]}
                        )
                return {"name": name, "children": children}

            # Assemble the final org chart
            org_chart = {
                "name": "SMG",
                "children": [
                    build_tree(clinic, dept_tree)
                    for clinic, dept_tree in sorted(hierarchy.items())
                ],
            }

            return custom_success_response(org_chart)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


CLINIC_SEGMENT_MAPPING = {
    "alpha healthcare international pte ltd": "Women's Health",
    "astra @ gleneagles": "Women's Health",
    "astra womens's specialists (jl) pte ltd": "Women's Health",
    "babies and children specialist clinic pte ltd": "Paediatrics",
    "children's clinic central pte ltd": "Paediatrics",
    "children's clinic international": "Paediatrics",
    "hsc cancer centre pte ltd": "Oncology",
    "kids clinic @ bishan pte ltd": "Paediatrics",
    "kids clinic @ punggol": "Paediatrics",
    "lifescan imaging @ farrer park": "Imaging",
    "lifescan imaging @ novena": "Imaging",
    "lifescan imaging @ paragon": "Imaging",
    "lifescan medical @ farrer park": "Others",
    "lifescan medical @ novena": "Others",
    "lifescan medical @ paragon": "Others",
    "lsc eye clinic": "Others",
    "smg aesthetic (downtown) pte ltd": "Aesthetics",
    "smg astra centre for women pte ltd": "Women's Health",
    "smg astra o&g pte ltd": "Women's Health",
    "smg heart centre pte ltd": "Others",
    "smg kids orthopaedic pte ltd": "Others",
    "smg o&g centre pte ltd": "Women's Health",
    "sw1 aesthetics": "Aesthetics",
    "sw1 plastic surgery pte ltd": "Aesthetics",
    "tck @ novena pte ltd": "Women's Health",
    "the breast clinic pte ltd": "Others",
    "the cancer centre pte ltd": "Oncology",
    "the dental studio @ bishan": "Others",
    "the dental studio @ oue": "Others",
    "the dental studio @ paragon": "Others",
    "the dental studio @ tai thong": "Others",
    "the obstetrics & gynaecology centre @ mt e orchard": "Women's Health",
    "the obstetrics & gynaecology centre @ novena": "Women's Health",
    "the women's specialist centre (hc) pte ltd": "Women's Health",
    "togc @ gleneagles pte ltd": "Women's Health",
    "vidaskin pte ltd": "Aesthetics",
    "wellness & gynaecology centre pte ltd": "Women's Health",
}


def get_segment_from_mapping(clinic_name):
    return CLINIC_SEGMENT_MAPPING.get(clinic_name.lower(), "Others")


def normalize_clinic_name(name: str) -> str:
    if not name:
        return ""

    name = name.lower()
    name = re.sub(r"[^\w\s]", " ", name)
    name = re.sub(r"\b(at|@)\b", " at ", name)
    name = re.sub(r"\s+", " ", name).strip()

    replacements = {
        "togc": "the obstetrics gynaecology centre",
        "smg": "",
        "centre": "center",
        "centres": "centers",
        "womens": "women's",
        "kid's": "kids",
        "tai thong": "tai tong",
    }
    for old, new in replacements.items():
        name = name.replace(old, new)

    return name.strip()


def match_clinic_segment(name, normalized_to_segment):
    # Step 1: Try exact match from hardcoded mapping
    mapped_segment = get_segment_from_mapping(name)
    if mapped_segment != "Others":
        return mapped_segment

    # Step 2: Fallback to normalized fuzzy DB match
    norm_name = normalize_clinic_name(name)
    match_result = process.extractOne(
        norm_name, normalized_to_segment.keys(), scorer=fuzz.token_sort_ratio
    )
    if match_result:
        match_name, score = match_result
        if score >= 70:
            return normalized_to_segment[match_name]

    return "Others"
